/*export const API_CONFIG = {
    // URL de base non utilisée (tu peux la supprimer si inutile)
    //URL: "http://172.18.41.87:86",

    // ✅ Utilise le proxy local vers le backend
    API_URL: "/api",

    TIMEOUT: 30000,
    HEADERS: {
        'Accept': 'application/json',
        'Access-Control-Allow-Credentials': 'true'
    }
};
 le code en haut Fonctionne. à remettre si ci-dessous plante - Nacer */
export const API_CONFIG = {
    // ✅ Utilise la variable d'environnement VITE_API_URL (HTTPS forcé)
    URL: 'http://127.0.0.1:8000',  // Fallback relatif si nécessaire
    API_URL: 'http://127.0.0.1:8000/api',  // Fallback relatif si nécessaire
    TIMEOUT: 30000,
    HEADERS: {
        'Accept': 'application/json',
     //   'Access-Control-Allow-Credentials': 'true'
    }
};