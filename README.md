# BarnaTrasteros Frontend

Frontend Vue.js para el sistema de gestión de alquileres BarnaTrasteros.

## 🚀 Características

- **Vue.js 3** con Composition API
- **Vue Router 4** para navegación
- **Pinia** para gestión de estado
- **Bootstrap 5** para UI/UX
- **Axios** para comunicación con API
- **Vite** como bundler
- **FontAwesome** para iconos
- **Chart.js** para gráficos
- **Vue Toastification** para notificaciones

## 📋 Funcionalidades

### Gestión de Propiedades
- ✅ Trasteros de alquiler
- ✅ Pisos de alquiler
- ✅ Estados de disponibilidad
- ✅ Información detallada

### Gestión de Clientes
- ✅ Registro de clientes
- ✅ Clientes potenciales
- ✅ Historial de alquileres

### Sistema de Pagos
- ✅ Verificación automática de pagos
- ✅ Estados: pendiente, al día, vencido, parcial
- ✅ Recargos por mora
- ✅ Historial de pagos

### Gestión de Documentos
- ✅ Subida de archivos
- ✅ Categorización por tipos
- ✅ Asociación con propiedades
- ✅ Estados de procesamiento

### Gastos del Edificio
- ✅ Registro de gastos
- ✅ Categorización
- ✅ Gastos recurrentes
- ✅ Control de vencimientos

### Dashboard y Reportes
- ✅ Resumen general
- ✅ Estadísticas de ocupación
- ✅ Estado de pagos
- ✅ Gráficos interactivos

## 🛠️ Instalación

### Prerrequisitos
- Node.js 16+ 
- npm o yarn
- Backend Laravel corriendo en http://localhost:8000

### Pasos de instalación

1. **Clonar el repositorio**
   ```bash
   git clone <repository-url>
   cd BarnaTrasteros_Frontend
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Configurar variables de entorno**
   ```bash
   cp .env.example .env
   # Editar .env con la configuración correcta
   ```

4. **Ejecutar en modo desarrollo**
   ```bash
   npm run dev
   ```

5. **Abrir en el navegador**
   ```
   http://localhost:3000
   ```

## 📁 Estructura del Proyecto

```
src/
├── assets/          # Recursos estáticos
│   ├── css/         # Estilos CSS
│   └── images/      # Imágenes
├── components/      # Componentes Vue reutilizables
│   ├── common/      # Componentes comunes
│   └── layout/      # Componentes de layout
├── router/          # Configuración de Vue Router
├── services/        # Servicios API
├── stores/          # Stores de Pinia
├── utils/           # Utilidades
├── views/           # Vistas/Páginas
│   ├── auth/        # Autenticación
│   ├── trasteros/   # Gestión de trasteros
│   ├── pisos/       # Gestión de pisos
│   ├── clientes/    # Gestión de clientes
│   ├── documentos/  # Gestión de documentos
│   ├── gastos/      # Gestión de gastos
│   ├── alquileres/  # Gestión de alquileres
│   └── reportes/    # Reportes y estadísticas
├── App.vue          # Componente principal
└── main.js          # Punto de entrada
```

## 🔧 Scripts Disponibles

```bash
# Desarrollo
npm run dev

# Build para producción
npm run build

# Preview del build
npm run preview

# Linting
npm run lint

# Formateo de código
npm run format
```

## 🌐 API Integration

El frontend se comunica con el backend Laravel a través de una API REST:

- **Base URL**: `http://localhost:8000/api`
- **Autenticación**: Laravel Sanctum (Bearer Token)
- **Formato**: JSON

### Endpoints principales:
- `POST /login` - Autenticación
- `GET /dashboard/*` - Datos del dashboard
- `CRUD /trasteros` - Gestión de trasteros
- `CRUD /pisos` - Gestión de pisos
- `CRUD /clientes` - Gestión de clientes
- `CRUD /documentos` - Gestión de documentos
- `CRUD /gastos` - Gestión de gastos
- `CRUD /alquileres` - Gestión de alquileres

## 🎨 Tecnologías UI/UX

- **Bootstrap 5**: Framework CSS responsivo
- **FontAwesome**: Biblioteca de iconos
- **Chart.js**: Gráficos interactivos
- **Vue Toastification**: Sistema de notificaciones
- **Custom CSS**: Estilos personalizados

## 🔐 Autenticación

- Sistema basado en tokens JWT
- Integración con Laravel Sanctum
- Persistencia en localStorage
- Guards de navegación automáticos
- Renovación automática de tokens

## 📱 Responsive Design

- Diseño mobile-first
- Breakpoints de Bootstrap 5
- Navegación adaptativa
- Tablas responsivas
- Formularios optimizados para móvil

## 🚀 Deployment

### Build para producción
```bash
npm run build
```

### Variables de entorno para producción
```bash
VITE_API_BASE_URL=https://tu-api.com/api
VITE_APP_ENV=production
VITE_DEBUG=false
```

## 🤝 Contribución

1. Fork del proyecto
2. Crear rama feature (`git checkout -b feature/nueva-funcionalidad`)
3. Commit cambios (`git commit -am 'Agregar nueva funcionalidad'`)
4. Push a la rama (`git push origin feature/nueva-funcionalidad`)
5. Crear Pull Request

## 📄 Licencia

Este proyecto es privado y propietario.

## 📞 Soporte

Para soporte técnico, contactar al equipo de desarrollo.
