<template>
  <div id="app">
    <!-- Navbar -->
    <NavBar v-if="isAuthenticated" />
    
    <!-- Contenido principal -->
    <main :class="{ 'with-navbar': isAuthenticated }">
      <router-view />
    </main>
    
    <!-- Loading overlay -->
    <LoadingOverlay v-if="isLoading" />
  </div>
</template>

<script>
import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import NavBar from '@/components/layout/NavBar.vue'
import LoadingOverlay from '@/components/common/LoadingOverlay.vue'

export default {
  name: 'App',
  components: {
    NavBar,
    LoadingOverlay
  },
  setup() {
    const authStore = useAuthStore()
    const appStore = useAppStore()

    // Verificar autenticación al cargar la app
    authStore.checkAuth()

    const isAuthenticated = computed(() => authStore.isAuthenticated)
    const isLoading = computed(() => appStore.isLoading)

    return {
      isAuthenticated,
      isLoading
    }
  }
}
</script>

<style>
#app {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  background-color: #f8f9fa;
}

main.with-navbar {
  margin-top: 70px; /* Altura del navbar */
  padding: 20px;
}

main:not(.with-navbar) {
  padding: 0;
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
