import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import api from '@/services/api'
import router from '@/router'
import { useToast } from 'vue-toastification'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const token = ref(localStorage.getItem('auth_token'))
  const isLoading = ref(false)

  // Getters
  const isAuthenticated = computed(() => !!token.value && !!user.value)

  // Actions
  const login = async (credentials) => {
    const toast = useToast()
    isLoading.value = true

    try {
      const response = await api.post('/login', credentials)
      const { user: userData, token: authToken } = response.data

      // Guardar datos de autenticación
      user.value = userData
      token.value = authToken
      localStorage.setItem('auth_token', authToken)

      // Configurar header de autorización
      api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`

      toast.success('¡Bienvenido!')
      router.push('/')

      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'Error al iniciar sesión'
      toast.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    const toast = useToast()
    isLoading.value = true

    try {
      if (token.value) {
        await api.post('/logout')
      }
    } catch (error) {
      console.error('Error al cerrar sesión:', error)
    } finally {
      // Limpiar datos locales
      user.value = null
      token.value = null
      localStorage.removeItem('auth_token')
      delete api.defaults.headers.common['Authorization']

      toast.info('Sesión cerrada')
      router.push('/login')
      isLoading.value = false
    }
  }

  const checkAuth = async () => {
    if (!token.value) {
      return false
    }

    try {
      // Configurar header de autorización
      api.defaults.headers.common['Authorization'] = `Bearer ${token.value}`

      // Verificar token con el servidor
      const response = await api.get('/user')
      user.value = response.data

      return true
    } catch (error) {
      console.error('Token inválido:', error)
      // Limpiar datos si el token es inválido
      user.value = null
      token.value = null
      localStorage.removeItem('auth_token')
      delete api.defaults.headers.common['Authorization']
      
      return false
    }
  }

  const register = async (userData) => {
    const toast = useToast()
    isLoading.value = true

    try {
      const response = await api.post('/register', userData)
      const { user: newUser, token: authToken } = response.data

      // Guardar datos de autenticación
      user.value = newUser
      token.value = authToken
      localStorage.setItem('auth_token', authToken)

      // Configurar header de autorización
      api.defaults.headers.common['Authorization'] = `Bearer ${authToken}`

      toast.success('¡Cuenta creada exitosamente!')
      router.push('/')

      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'Error al crear la cuenta'
      toast.error(message)
      return { success: false, message }
    } finally {
      isLoading.value = false
    }
  }

  const updateUser = (userData) => {
    user.value = { ...user.value, ...userData }
  }

  return {
    // State
    user,
    token,
    isLoading,
    
    // Getters
    isAuthenticated,
    
    // Actions
    login,
    logout,
    checkAuth,
    register,
    updateUser
  }
})
