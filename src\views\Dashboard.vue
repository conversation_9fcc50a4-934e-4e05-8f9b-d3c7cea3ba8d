<template>
  <div class="dashboard">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Dashboard</h1>
              <p class="text-muted mb-0">Resumen general del sistema</p>
            </div>
            <div>
              <button class="btn btn-outline-primary" @click="refreshData">
                <i class="fas fa-sync-alt" :class="{ 'fa-spin': isLoading }"></i>
                Actualizar
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Cards de resumen -->
      <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                    Trasteros
                  </div>
                  <div class="h5 mb-0 font-weight-bold text-gray-800">
                    {{ resumen.propiedades?.trasteros_ocupados || 0 }} / {{ resumen.propiedades?.trasteros_total || 0 }}
                  </div>
                  <div class="text-xs text-muted">
                    {{ calcularPorcentaje(resumen.propiedades?.trasteros_ocupados, resumen.propiedades?.trasteros_total) }}% ocupación
                  </div>
                </div>
                <div class="col-auto">
                  <i class="fas fa-warehouse fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                    Pisos
                  </div>
                  <div class="h5 mb-0 font-weight-bold text-gray-800">
                    {{ resumen.propiedades?.pisos_ocupados || 0 }} / {{ resumen.propiedades?.pisos_total || 0 }}
                  </div>
                  <div class="text-xs text-muted">
                    {{ calcularPorcentaje(resumen.propiedades?.pisos_ocupados, resumen.propiedades?.pisos_total) }}% ocupación
                  </div>
                </div>
                <div class="col-auto">
                  <i class="fas fa-building fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                    Clientes
                  </div>
                  <div class="h5 mb-0 font-weight-bold text-gray-800">
                    {{ resumen.clientes?.activos || 0 }}
                  </div>
                  <div class="text-xs text-muted">
                    {{ resumen.clientes?.posibles || 0 }} posibles
                  </div>
                </div>
                <div class="col-auto">
                  <i class="fas fa-users fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
          <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
              <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                  <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                    Pagos Pendientes
                  </div>
                  <div class="h5 mb-0 font-weight-bold text-gray-800">
                    {{ resumen.pagos?.pendientes || 0 }}
                  </div>
                  <div class="text-xs text-muted">
                    {{ resumen.pagos?.vencidos || 0 }} vencidos
                  </div>
                </div>
                <div class="col-auto">
                  <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Gráficos y tablas -->
      <div class="row">
        <!-- Pagos pendientes -->
        <div class="col-xl-8 col-lg-7">
          <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
              <h6 class="m-0 font-weight-bold text-primary">Pagos Pendientes</h6>
              <router-link to="/alquileres" class="btn btn-sm btn-primary">
                Ver todos
              </router-link>
            </div>
            <div class="card-body">
              <div v-if="pagosPendientes.alquileres_pendientes?.length > 0" class="table-responsive">
                <table class="table table-bordered">
                  <thead>
                    <tr>
                      <th>Cliente</th>
                      <th>Propiedad</th>
                      <th>Importe</th>
                      <th>Vencimiento</th>
                      <th>Estado</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="pago in pagosPendientes.alquileres_pendientes.slice(0, 5)" :key="pago.id">
                      <td>{{ pago.cliente?.nombre }}</td>
                      <td>{{ pago.alquilable_type === 'App\\Trastero' ? 'Trastero' : 'Piso' }} #{{ pago.alquilable?.numero || pago.alquilable?.id }}</td>
                      <td>{{ formatCurrency(pago.valor) }}</td>
                      <td>{{ formatDate(pago.fecha_vencimiento) }}</td>
                      <td>
                        <span :class="getEstadoPagoClass(pago.estado_pago)">
                          {{ pago.estado_pago }}
                        </span>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <div v-else class="text-center py-4">
                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                <p class="text-muted">No hay pagos pendientes</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Estadísticas rápidas -->
        <div class="col-xl-4 col-lg-5">
          <div class="card shadow mb-4">
            <div class="card-header py-3">
              <h6 class="m-0 font-weight-bold text-primary">Estadísticas Rápidas</h6>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <div class="small text-gray-500">Documentos</div>
                <div class="h5 mb-0">{{ resumen.documentos?.total || 0 }}</div>
                <div class="text-xs">
                  <span class="text-warning">{{ resumen.documentos?.pendientes || 0 }} pendientes</span>
                </div>
              </div>
              
              <div class="mb-3">
                <div class="small text-gray-500">Gastos Pendientes</div>
                <div class="h5 mb-0">{{ resumen.gastos?.pendientes || 0 }}</div>
                <div class="text-xs">
                  <span class="text-danger">{{ resumen.gastos?.vencidos || 0 }} vencidos</span>
                </div>
              </div>

              <div class="mb-3">
                <div class="small text-gray-500">Total Pendiente</div>
                <div class="h5 mb-0 text-warning">{{ formatCurrency(pagosPendientes.total_pendiente) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import api from '@/services/api'
import { useToast } from 'vue-toastification'

export default {
  name: 'Dashboard',
  setup() {
    const toast = useToast()
    const isLoading = ref(false)
    const resumen = ref({})
    const pagosPendientes = ref({})

    const loadDashboardData = async () => {
      isLoading.value = true
      
      try {
        const [resumenResponse, pagosResponse] = await Promise.all([
          api.get('/dashboard/resumen'),
          api.get('/dashboard/pagos-pendientes')
        ])
        
        resumen.value = resumenResponse.data
        pagosPendientes.value = pagosResponse.data
      } catch (error) {
        console.error('Error cargando dashboard:', error)
        toast.error('Error al cargar los datos del dashboard')
      } finally {
        isLoading.value = false
      }
    }

    const refreshData = () => {
      loadDashboardData()
    }

    const calcularPorcentaje = (ocupados, total) => {
      if (!total || total === 0) return 0
      return Math.round((ocupados / total) * 100)
    }

    const formatCurrency = (amount) => {
      if (!amount) return '€0,00'
      return new Intl.NumberFormat('es-ES', {
        style: 'currency',
        currency: 'EUR'
      }).format(amount)
    }

    const formatDate = (date) => {
      if (!date) return '-'
      return new Date(date).toLocaleDateString('es-ES')
    }

    const getEstadoPagoClass = (estado) => {
      const classes = {
        'pendiente': 'badge bg-warning',
        'vencido': 'badge bg-danger',
        'al_dia': 'badge bg-success',
        'parcial': 'badge bg-info'
      }
      return classes[estado] || 'badge bg-secondary'
    }

    onMounted(() => {
      loadDashboardData()
    })

    return {
      isLoading,
      resumen,
      pagosPendientes,
      refreshData,
      calcularPorcentaje,
      formatCurrency,
      formatDate,
      getEstadoPagoClass
    }
  }
}
</script>

<style scoped>
.border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
  border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
  border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.text-gray-300 {
  color: #dddfeb !important;
}

.text-gray-500 {
  color: #858796 !important;
}

.text-gray-800 {
  color: #5a5c69 !important;
}
</style>
