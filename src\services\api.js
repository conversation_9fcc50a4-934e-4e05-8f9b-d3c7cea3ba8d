import axios from 'axios'
import { useToast } from 'vue-toastification'

// Configuración base de axios
const api = axios.create({
  baseURL: 'http://127.0.0.1:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: false
})

// Función para obtener el CSRF token
const getCSRFToken = async () => {
  try {
    const response = await axios.get('http://127.0.0.1:8000/api/csrf-token', {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })

    console.log('Respuesta completa del endpoint CSRF:', response.data)
    console.log('Tipo de respuesta:', typeof response.data)

    // Intentar obtener el token desde diferentes fuentes
    let token = null

    // 1. Si la respuesta es directamente el token (string)
    if (typeof response.data === 'string' && response.data.length > 10) {
      token = response.data
      console.log('Token CSRF recibido como string directo')
    }
    // 2. Desde el cuerpo de la respuesta (método preferido)
    else if (response.data && response.data.csrf_token) {
      token = response.data.csrf_token
      console.log('Token CSRF extraído de response.data.csrf_token')
    }
    // 3. Desde el header X-CSRF-TOKEN
    else if (response.headers['x-csrf-token']) {
      token = response.headers['x-csrf-token']
      console.log('Token CSRF extraído del header X-CSRF-TOKEN')
    }
    // 4. Otros formatos comunes en la respuesta
    else if (response.data && response.data.token) {
      token = response.data.token
      console.log('Token CSRF extraído de response.data.token')
    }
    // 5. Formato Laravel _token
    else if (response.data && response.data._token) {
      token = response.data._token
      console.log('Token CSRF extraído de response.data._token')
    }

    if (token) {
      console.log('CSRF token obtenido exitosamente:', token.substring(0, 10) + '...')
    } else {
      console.warn('No se pudo obtener el CSRF token desde la respuesta del servidor')
      console.log('Estructura de respuesta disponible:', Object.keys(response.data || {}))
    }

    return token
  } catch (error) {
    console.error('Error obteniendo CSRF token:', error)
    return null
  }
}

// Interceptor para requests
api.interceptors.request.use(
  async (config) => {
    // Agregar token de autenticación si existe
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // Agregar CSRF token para métodos que lo requieren (solo si está habilitado)
    if (csrfEnabled && ['post', 'put', 'patch', 'delete'].includes(config.method.toLowerCase())) {
      let csrfToken = localStorage.getItem('csrf_token')

      // Si no hay token CSRF guardado, obtenerlo
      if (!csrfToken) {
        console.log('No hay CSRF token guardado, obteniendo uno nuevo...')
        csrfToken = await getCSRFToken()
        if (csrfToken) {
          localStorage.setItem('csrf_token', csrfToken)
          console.log('Nuevo CSRF token obtenido y guardado')
        }
      }

      if (csrfToken) {
        config.headers['X-CSRF-TOKEN'] = csrfToken
        console.log(`Enviando petición ${config.method.toUpperCase()} a ${config.url} con CSRF token:`, csrfToken.substring(0, 10) + '...')
      } else {
        console.warn(`Petición ${config.method.toUpperCase()} a ${config.url} sin CSRF token`)
      }
    } else if (!csrfEnabled && ['post', 'put', 'patch', 'delete'].includes(config.method.toLowerCase())) {
      console.log(`Petición ${config.method.toUpperCase()} a ${config.url} - CSRF deshabilitado`)
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Interceptor para responses
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const toast = useToast()
    
    // Manejar errores comunes
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Token expirado o inválido
          localStorage.removeItem('auth_token')
          delete api.defaults.headers.common['Authorization']

          if (window.location.pathname !== '/login') {
            toast.error('Sesión expirada. Por favor, inicia sesión nuevamente.')
            window.location.href = '/login'
          }
          break

        case 419:
          if (csrfEnabled) {
            // CSRF token mismatch - obtener nuevo token y reintentar (solo una vez)
            if (!error.config._csrfRetry) {
              console.log('Error 419: Token CSRF inválido, obteniendo nuevo token...')
              localStorage.removeItem('csrf_token')
              const newCSRFToken = await getCSRFToken()
              if (newCSRFToken) {
                localStorage.setItem('csrf_token', newCSRFToken)
                // Marcar que ya se reintentó para evitar bucles infinitos
                error.config._csrfRetry = true
                error.config.headers['X-CSRF-TOKEN'] = newCSRFToken
                console.log('Reintentando petición con nuevo CSRF token:', newCSRFToken.substring(0, 10) + '...')
                return api.request(error.config)
              }
            }
            console.error('Error 419: No se pudo obtener un token CSRF válido después del reintento')
            toast.error('Error de seguridad. Por favor, recarga la página.')
          } else {
            console.error('Error 419: El backend requiere CSRF token pero está deshabilitado en el frontend')
            toast.error('Error de configuración: El backend requiere CSRF token. Contacta al administrador.')
          }
          break
          
        case 403:
          toast.error('No tienes permisos para realizar esta acción')
          break
          
        case 404:
          toast.error('Recurso no encontrado')
          break
          
        case 422:
          // Errores de validación
          if (data.errors) {
            Object.values(data.errors).flat().forEach(error => {
              toast.error(error)
            })
          } else if (data.message) {
            toast.error(data.message)
          }
          break
          
        case 500:
          toast.error('Error interno del servidor')
          break
          
        default:
          toast.error(data.message || 'Ha ocurrido un error')
      }
    } else if (error.request) {
      // Error de red
      toast.error('Error de conexión. Verifica tu conexión a internet.')
    } else {
      // Error de configuración
      toast.error('Error en la configuración de la solicitud')
    }
    
    return Promise.reject(error)
  }
)

// Función para inicializar CSRF token al cargar la aplicación
export const initializeCSRF = async () => {
  console.log('Inicializando CSRF token...')
  const csrfToken = await getCSRFToken()
  if (csrfToken) {
    localStorage.setItem('csrf_token', csrfToken)
    console.log('CSRF token obtenido y guardado:', csrfToken.substring(0, 10) + '...')
  } else {
    console.warn('No se pudo obtener el CSRF token')
  }
  return csrfToken
}

// Función para debug - verificar el estado del CSRF token
export const debugCSRF = () => {
  const storedToken = localStorage.getItem('csrf_token')
  console.log('CSRF token almacenado:', storedToken ? storedToken.substring(0, 10) + '...' : 'No encontrado')
  return storedToken
}

// Función para probar manualmente el endpoint CSRF
export const testCSRFEndpoint = async () => {
  try {
    console.log('Probando endpoint CSRF...')
    const response = await axios.get('http://127.0.0.1:8000/api/csrf-token', {
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    })
    console.log('Respuesta del endpoint CSRF:', response.data)
    console.log('Headers de respuesta:', response.headers)
    return response.data
  } catch (error) {
    console.error('Error probando endpoint CSRF:', error)
    return null
  }
}

// Variable para controlar si CSRF está habilitado
let csrfEnabled = true

// Función para habilitar/deshabilitar CSRF
export const toggleCSRF = (enabled = true) => {
  csrfEnabled = enabled
  console.log(`CSRF ${enabled ? 'habilitado' : 'deshabilitado'}`)
  return csrfEnabled
}

// Añadir funciones de debug al objeto window para desarrollo
if (typeof window !== 'undefined') {
  window.debugCSRF = debugCSRF
  window.testCSRFEndpoint = testCSRFEndpoint
  window.initializeCSRF = initializeCSRF
  window.toggleCSRF = toggleCSRF
  window.csrfEnabled = () => csrfEnabled
}

export default api
